import React, { useState, useEffect } from 'react';
import {
    View,
    Text,
    StyleSheet,
    ScrollView,
    TouchableOpacity,
    TextInput,
    Modal,
    FlatList,
    Alert,
} from 'react-native';
import { Dropdown } from 'react-native-element-dropdown';
import CheckBox from '@react-native-community/checkbox';
import { DataTable } from 'react-native-paper';
import Navbar from '../../components/Navbar';
import { bearerToken, loginBranchID, branchName, loginUserID } from '../../globals';
import { fetchBranchList } from '../../apiHandling/StockAPI/fetchBranchListAPI';
import { fetchBranchDetails } from '../../apiHandling/StockAPI/fetchBranchDetailsAPI';
import { fetchVehicleList } from '../../apiHandling/StockAPI/fetchVehicleAPI';
import { fetchEmployeeList } from '../../apiHandling/StockAPI/fetchEmployeeAPI';
import { fetchItemList } from '../../apiHandling/StockAPI/fetchItemListAPI';
import { fetchItemDetail, fetchItemStock } from '../../apiHandling/StockAPI/itemDetailsStockAPI';
import { saveReceiving, fetchBusinessDate } from '../../apiHandling/StockAPI/saveReceivingAPI';

const ReceivingScreen = () => {
    const [currentTime, setCurrentTime] = useState('');
    const [selectedMenu, setSelectedMenu] = useState('Stock');
    const [selectedScrollOption, setSelectedScrollOption] = useState('');
    const [selectedTransferType, setSelectedTransferType] = useState('');
    const [orderNumber, setOrderNumber] = useState('');
    const [invoiceNumber, setInvoiceNumber] = useState('');
    const [lotNumber, setLotNumber] = useState('');

    // Location & Vehicle Details states
    const [location, setLocation] = useState('');
    const [selectedBranchId, setSelectedBranchId] = useState('');
    const [selectedBranchName, setSelectedBranchName] = useState('');
    const [isTransit, setIsTransit] = useState(false);
    const [selectedVehicleType, setSelectedVehicleType] = useState('');
    const [selectedAddress, setSelectedAddress] = useState('');
    const [distance, setDistance] = useState('');
    const [vehicleNumber, setVehicleNumber] = useState('');
    const [driverName, setDriverName] = useState('');
    const [remarks, setRemarks] = useState('');

    // Location Modal states
    const [isLocationModalVisible, setIsLocationModalVisible] = useState(false);
    const [branchList, setBranchList] = useState([]);
    const [loadingBranches, setLoadingBranches] = useState(false);

    // Address, Vehicle, and Employee states
    const [addressList, setAddressList] = useState([]);
    const [vehicleList, setVehicleList] = useState([]);
    const [employeeList, setEmployeeList] = useState([]);
    const [loadingAddresses, setLoadingAddresses] = useState(false);
    const [loadingVehicles, setLoadingVehicles] = useState(false);
    const [loadingEmployees, setLoadingEmployees] = useState(false);

    // Item Details states
    const [itemName, setItemName] = useState('');
    const [itemId, setItemId] = useState('');
    const [itemBatch, setItemBatch] = useState('');
    const [selectedPort, setSelectedPort] = useState('');
    const [itemWeight, setItemWeight] = useState('');
    const [itemNos, setItemNos] = useState('');

    // Item management states
    const [itemList, setItemList] = useState([]);
    const [batchList, setBatchList] = useState([]);
    const [itemDetail, setItemDetail] = useState(null);
    const [batchEnabled, setBatchEnabled] = useState(false);
    const [sellByWeight, setSellByWeight] = useState(false);
    const [altQtyEnabled, setAltQtyEnabled] = useState(false);

    // Modal states
    const [modalVisible, setModalVisible] = useState(false);
    const [batchModalVisible, setBatchModalVisible] = useState(false);
    const [searchText, setSearchText] = useState('');

    // Table states
    const [tableData, setTableData] = useState([]);
    const [selectedRows, setSelectedRows] = useState([]);

    const businessDate = new Date().toLocaleDateString();

    const menuOptions = [
        'Billing',
        'Stock',
        'Logistics',
        'Finance',
        'HR',
        'Utils',
        'Reports',
        'Exit',
    ];

    useEffect(() => {
        const interval = setInterval(() => {
            const now = new Date();
            setCurrentTime(now.toLocaleTimeString());
        }, 1000);
        return () => clearInterval(interval);
    }, []);

    useEffect(() => {
        // Load item list when screen mounts
        loadItemList();
    }, []);

    const loadItemList = async () => {
        const data = await fetchItemList(bearerToken, loginBranchID);
        setItemList(data);
    };

    // Item selection functions
    const handleItemSelectPress = () => {
        // Reset all item-related state
        setItemName('');
        setItemId('');
        setItemBatch('');
        setBatchList([]);
        setItemNos('');
        setItemWeight('');
        setBatchEnabled(false);
        setSellByWeight(false);
        setAltQtyEnabled(false);
        setSearchText('');

        // Open item selection modal
        setModalVisible(true);
    };

    const handleItemSelect = async (item) => {
        setItemName(item.ItemName);
        setItemId(item.ItemID);
        setModalVisible(false);

        const detail = await fetchItemDetail(bearerToken, item.ItemID);
        setItemDetail(detail);

        if (!detail) {
            // Default assumption: batch is disabled, sell by weight is false, alt qty is false
            setBatchEnabled(false);
            setSellByWeight(false);
            setAltQtyEnabled(false);
            setItemNos('0');
            setItemWeight('0');
            setBatchList([]);
            setItemBatch('');
            return;
        }

        const { BatchEnabled, SellByWeight, AltQtyEnabled } = detail;

        setBatchEnabled(BatchEnabled);
        setSellByWeight(SellByWeight);
        setAltQtyEnabled(AltQtyEnabled);

        if (BatchEnabled) {
            const stockData = await fetchItemStock(bearerToken, loginBranchID, item.ItemID);
            setBatchList(stockData);
            setItemBatch('');

            // Reset fields when batch is enabled
            if (SellByWeight && AltQtyEnabled) {
                setItemWeight('');
                setItemNos('');
            } else if (SellByWeight) {
                setItemWeight('');
                setItemNos('0');
            } else {
                setItemNos('');
                setItemWeight('0');
            }
        } else {
            // No batch, set fields based on flags
            if (SellByWeight && AltQtyEnabled) {
                setItemWeight('');
                setItemNos('');
            } else if (SellByWeight) {
                setItemWeight('');
                setItemNos('0');
            } else {
                setItemNos('');
                setItemWeight('0');
            }
        }
    };

    const handleBatchSelect = (batchItem) => {
        setItemBatch(batchItem.BatchNumber);
        setBatchModalVisible(false);

        // Auto-fill based on flags - but for receiving we don't need stock quantities
        if (sellByWeight && altQtyEnabled) {
            setItemWeight('');
            setItemNos('');
        } else if (sellByWeight) {
            setItemWeight('');
            setItemNos('0');
        } else {
            setItemNos('');
            setItemWeight('0');
        }
    };

    // Table management functions
    const handleAddItem = () => {
        if (!itemName || !itemId) {
            Alert.alert('Error', 'Please select an item first');
            return;
        }

        if (batchEnabled && !itemBatch) {
            Alert.alert('Error', 'Please select a batch');
            return;
        }

        // Check for duplicates
        const isDuplicate = tableData.some(item =>
            item.itemId === itemId &&
            (!batchEnabled || item.batch === itemBatch)
        );

        if (isDuplicate) {
            Alert.alert('Error', 'Item already added with same batch');
            return;
        }

        const newItem = {
            lineNo: String(tableData.length + 1).padStart(3, '0'),
            itemId,
            itemName,
            batch: itemBatch || '',
            nos: itemNos || '0',
            kgs: itemWeight || '0',
            remarks: remarks || '',
            id: Date.now(), // Unique row ID
            selected: false,
            // Store item details for save API
            sellByWeight: sellByWeight,
            altQtyEnabled: altQtyEnabled,
            batchEnabled: batchEnabled,
            itemFamilyID: itemDetail?.ItemFamilyID || '',
            stockGroupId: itemDetail?.StockGroupId || ''
        };

        setTableData(prev => [...prev, newItem]);
        handleClearFields(); // Reset form after add
    };

    const handleClearFields = () => {
        setItemName('');
        setItemId('');
        setItemBatch('');
        setBatchList([]);
        setItemNos('');
        setItemWeight('');
        setRemarks('');
        setBatchEnabled(false);
        setSellByWeight(false);
        setAltQtyEnabled(false);
        setSelectedPort(null);
        setItemDetail(null);
    };

    const deleteSelectedItems = () => {
        const filtered = tableData.filter(item => !selectedRows.includes(item.id));
        const reIndexed = filtered.map((item, index) => ({
            ...item,
            lineNo: String(index + 1).padStart(3, '0'),
        }));
        setTableData(reIndexed);
        setSelectedRows([]);
    };

    const handleSave = async () => {
        try {
            // Validation
            if (!selectedTransferType) {
                Alert.alert('Error', 'Please select transfer type');
                return;
            }
            if (!selectedBranchId) {
                Alert.alert('Error', 'Please select location');
                return;
            }
            if (tableData.length === 0) {
                Alert.alert('Error', 'Please add at least one item');
                return;
            }

            const businessDate = await fetchBusinessDate(bearerToken, loginBranchID);
            const systemDate = new Date().toISOString();

            const payload = {
                trOutId: "",
                branchId: loginBranchID,
                transTypeId: "",
                tranSubTypeId: selectedTransferType,
                taxStructureCode: "STATEGST",
                businessDate: businessDate,
                trOrderId: orderNumber || "",
                remarks: remarks || "",
                toBranchId: selectedBranchId,
                isO_Number: invoiceNumber || "",
                delieveryMode: "",
                vehicleOwnerType: selectedVehicleType?.toLowerCase() || "own",
                vehicleOwnerBranchId: loginBranchID,
                deliveryStatusCode: "",
                driverName: driverName || "",
                deliveryAddressID: selectedAddress || "00001",
                kmDistance: parseInt(distance) || 0,
                refIndentId: "",
                vehicleNumber: vehicleNumber || "",
                deleted: "N",
                posted: true,
                isKISettled: true,
                createdUserId: loginUserID,
                createdDate: systemDate,
                modifiedUserId: "string",
                modifiedDate: "1753-01-01T00:00:00.000Z",
                deletedUserId: "string",
                deletedDate: "1753-01-01T00:00:00.000Z",
                postedUserID: "string",
                postedDate: "1753-01-01T00:00:00.000Z",
                trOutDetails: tableData.map(item => ({
                    lineNumber: item.lineNo,
                    itemName: item.itemName,
                    batchNumber: item.batch || "",
                    nos: item.nos || "",
                    kgs: item.kgs || "",
                    altQty: 0,
                    qty: parseFloat(item.kgs) || parseFloat(item.nos) || 0,
                    remarks: item.remarks || "",
                    stockQty: 0,
                    stockAltQty: 0,
                    wScale: 0,
                    sellByWeight: item.sellByWeight || false,
                    unitGrossWt: 0,
                    altQtyEnabled: item.altQtyEnabled || false,
                    itemID: item.itemId,
                    groupStockQty: 0,
                    itemStatusId: "OK",
                    binID: "OKBIN",
                    rate: 0,
                    taxCategoryId: "0000000002",
                    taxPercent: 0,
                    unitMRP: 0,
                    productAmount: 0,
                    totalAmount: 0,
                    itemFamilyId: item.itemFamilyID || "",
                    stockGroupId: item.stockGroupId || "",
                    batchEnabled: item.batchEnabled || false,
                    deleted: "N",
                    batchUseByDate: "1753-01-01T00:00:00.000Z",
                    createdUserId: loginUserID,
                    createdDate: systemDate,
                    modifiedUserId: "string",
                    modifiedDate: "1753-01-01T00:00:00.000Z",
                    deletedUserId: "string",
                    deletedDate: "1753-01-01T00:00:00.000Z",
                    dml: "i"
                }))
            };

            const result = await saveReceiving(bearerToken, payload);

            if (result.result === 1) {
                Alert.alert('Success', 'Receiving data saved successfully');
                // Clear form after successful save
                setTableData([]);
                setSelectedRows([]);
                handleClearFields();
            } else {
                Alert.alert('Save Failed', result.description || 'Unexpected response from server');
            }
        } catch (error) {
            console.error('Save error:', error);
            Alert.alert('Error', 'Failed to save receiving data');
        }
    };

    const handleMenuPress = (option) => {
        if (option === 'Exit') {
            // Replace with navigation to login
            console.log('Navigating to login screen...');
        } else {
            setSelectedMenu(option);
        }
    };

    // Function to load branch list
    const loadBranchList = async () => {
        try {
            setLoadingBranches(true);
            const branches = await fetchBranchList(bearerToken);
            setBranchList(branches);
        } catch (error) {
            console.error('Error loading branch list:', error);
            Alert.alert('Error', 'Failed to load branch list');
        } finally {
            setLoadingBranches(false);
        }
    };

    // Function to handle location select button press
    const handleLocationSelectPress = () => {
        setIsLocationModalVisible(true);
        if (branchList.length === 0) {
            loadBranchList();
        }
    };

    // Function to load branch details (addresses)
    const loadBranchDetails = async (branchId) => {
        try {
            setLoadingAddresses(true);
            const addresses = await fetchBranchDetails(bearerToken, branchId);
            setAddressList(addresses);
        } catch (error) {
            console.error('Error loading branch details:', error);
            Alert.alert('Error', 'Failed to load branch addresses');
        } finally {
            setLoadingAddresses(false);
        }
    };

    // Function to load vehicle list
    const loadVehicleList = async (branchId) => {
        try {
            setLoadingVehicles(true);
            const vehicles = await fetchVehicleList(bearerToken, branchId);
            setVehicleList(vehicles);
        } catch (error) {
            console.error('Error loading vehicle list:', error);
            Alert.alert('Error', 'Failed to load vehicle list');
        } finally {
            setLoadingVehicles(false);
        }
    };

    // Function to load employee list
    const loadEmployeeList = async () => {
        try {
            setLoadingEmployees(true);
            const employees = await fetchEmployeeList(bearerToken, loginBranchID);
            setEmployeeList(employees);
        } catch (error) {
            console.error('Error loading employee list:', error);
            Alert.alert('Error', 'Failed to load employee list');
        } finally {
            setLoadingEmployees(false);
        }
    };

    // Function to handle branch selection
    const handleBranchSelection = async (branch) => {
        setSelectedBranchId(branch.branchID);
        setSelectedBranchName(branch.branchName);
        setLocation(`${branch.branchID} - ${branch.branchName}`);
        setIsLocationModalVisible(false);

        // Load related data when branch is selected
        await loadBranchDetails(branch.branchID);

        // Load vehicles based on vehicle type selection
        if (selectedVehicleType === 'Own') {
            await loadVehicleList(loginBranchID);
        } else {
            await loadVehicleList(branch.branchID);
        }

        // Load employees (always from login branch)
        await loadEmployeeList();
    };

    // Function to close location modal
    const closeLocationModal = () => {
        setIsLocationModalVisible(false);
    };
    return (
        <View style={styles.container}>
            {/* <Navbar/> */}
            <Navbar/>
            {/* Scroll Options */}
            <View style={styles.scrollOptions_container}>

                <View style={styles.scrollOptions_row}>
                    {/* Back Arrow with Receiving Text */}
                    <TouchableOpacity style={styles.scrollOptions_backContainer}>
                        {/*<Text style={styles.scrollOptions_backArrow}>←</Text>*/}
                        <Text style={styles.scrollOptions_screenTitle}>Receiving</Text>
                    </TouchableOpacity>

                    {/* Action Buttons */}
                    <View style={styles.scrollOptions_buttonsContainer}>
                        {['New', 'Save', 'View', 'Cancel'].map((option, index) => {
                            const isSelected = selectedScrollOption === option;
                            let buttonStyle = [styles.scrollOptions_button];

                            if (option === 'Cancel') {
                                buttonStyle.push({
                                    backgroundColor: isSelected ? '#FE0000' : '#FF3333',
                                });
                            } else if (option === 'Save') {
                                buttonStyle.push({
                                    backgroundColor: isSelected ? '#02720F' : '#02A515',
                                });
                            } else {
                                buttonStyle.push({
                                    backgroundColor: isSelected ? '#02096A' : '#DEDDDD',
                                });
                            }

                            return (
                                <View style={styles.scrollOptions_buttonWrapper} key={index}>
                                    <TouchableOpacity
                                        style={buttonStyle}
                                        onPress={() => {
                                            setSelectedScrollOption(option);
                                            if (option === 'Save') {
                                                handleSave();
                                            }
                                        }}
                                    >
                                        <Text style={[
                                            styles.scrollOptions_buttonText,
                                            { color: isSelected ? 'white' : 'black' },
                                        ]}>
                                            {option}
                                        </Text>
                                    </TouchableOpacity>
                                </View>
                            );
                        })}
                    </View>
                </View>
            </View>
            {/* Indent Details Container */}
            <View style={styles.indentDetails_container}>
                {/* Row 1: Indent Details Title */}
                <View style={styles.indentDetails_row}>
                    <Text style={styles.indentDetails_title}>Indent details</Text>
                </View>

                {/* Row 2: Transfer Type Dropdown, Choose Order, Select Button */}
                <View style={styles.indentDetails_inputRow}>
                    <View style={styles.indentDetails_transferTypeContainer}>
                        <Dropdown
                            data={[
                                { label: 'IB Branch Transfer', value: 'IB ST' },
                             
                            ]}
                            labelField="label"
                            valueField="value"
                            placeholder="Select Transfer Type"
                            value={selectedTransferType}
                            onChange={(item) => setSelectedTransferType(item.value)}
                            style={styles.indentDetails_dropdown}
                        />
                    </View>

                    <TextInput
                        style={styles.indentDetails_textField}
                        placeholder="Choose order"
                        value={orderNumber}
                        onChangeText={setOrderNumber}
                    />

                    <TouchableOpacity style={styles.indentDetails_selectButton}>
                        <Text style={styles.indentDetails_selectButtonText}>Select</Text>
                    </TouchableOpacity>
                </View>

                {/* Row 3: Invoice, Lot */}
                <View style={styles.indentDetails_inputRow}>
                    <TextInput
                        style={styles.indentDetails_textField}
                        placeholder="Invoice"
                        value={invoiceNumber}
                        onChangeText={setInvoiceNumber}
                    />

                    <TextInput
                        style={styles.indentDetails_textField}
                        placeholder="Lot"
                        value={lotNumber}
                        onChangeText={setLotNumber}
                    />
                </View>
            </View>

            {/* Location & Vehicle Details Container */}
            <View style={styles.locationVehicle_container}>
                {/* Row 1: Location & Vehicle Details Title */}
                <View style={styles.locationVehicle_row}>
                    <Text style={styles.locationVehicle_title}>Location & Vehicle details</Text>
                </View>

                {/* Row 2: Choose Location, Select Button, Transit Button */}
                <View style={styles.locationVehicle_inputRow}>
                    <TextInput
                        style={styles.locationVehicle_textField}
                        placeholder="Choose location"
                        value={location}
                        onChangeText={setLocation}
                    />

                    <TouchableOpacity
                        style={styles.locationVehicle_selectButton}
                        onPress={handleLocationSelectPress}
                    >
                        <Text style={styles.locationVehicle_buttonText}>Select</Text>
                    </TouchableOpacity>

                    <TouchableOpacity
                        style={[
                            styles.locationVehicle_transitButton,
                            { backgroundColor: isTransit ? '#02096A' : '#DEDDDD' }
                        ]}
                        onPress={() => setIsTransit(!isTransit)}
                    >
                        <Text style={[
                            styles.locationVehicle_buttonText,
                            { color: isTransit ? 'white' : 'black' }
                        ]}>
                            Transit
                        </Text>
                    </TouchableOpacity>
                </View>

                {/* Row 3: Vehicle Type Dropdown, Address Dropdown, New Button */}
                <View style={styles.locationVehicle_inputRow}>
                    <View style={styles.locationVehicle_dropdownContainer}>
                        <Dropdown
                            data={[
                                { label: 'Own', value: 'Own' },
                                { label: 'Transporter', value: 'Transporter' },
                            ]}
                            labelField="label"
                            valueField="value"
                            placeholder="Select vehicle type"
                            value={selectedVehicleType}
                            onChange={async (item) => {
                                setSelectedVehicleType(item.value);
                                // Load vehicles based on selection
                                if (selectedBranchId) {
                                    if (item.value === 'Own') {
                                        await loadVehicleList(loginBranchID);
                                    } else {
                                        await loadVehicleList(selectedBranchId);
                                    }
                                }
                            }}
                            style={styles.locationVehicle_dropdown}
                        />
                    </View>

                    <View style={styles.locationVehicle_dropdownContainer}>
                        <Dropdown
                            data={addressList.map(address => ({
                                label: `${address.AddressID} - ${address.AreaName}`,
                                value: address.AddressID
                            }))}
                            labelField="label"
                            valueField="value"
                            placeholder="Address"
                            value={selectedAddress}
                            onChange={(item) => setSelectedAddress(item.value)}
                            style={styles.locationVehicle_dropdown}
                        />
                    </View>

                    <TouchableOpacity style={styles.locationVehicle_newButton}>
                        <Text style={styles.locationVehicle_buttonText}>New</Text>
                    </TouchableOpacity>
                </View>

                {/* Row 4: Distance, Vehicle Number, Driver */}
                <View style={styles.locationVehicle_inputRow}>
                    <TextInput
                        style={styles.locationVehicle_textField}
                        placeholder="Distance"
                        value={distance}
                        onChangeText={setDistance}
                        keyboardType="numeric"
                    />

                    <View style={styles.locationVehicle_dropdownContainer}>
                        <Dropdown
                            data={vehicleList.map(vehicle => ({
                                label: vehicle.vehicleId || vehicle.VehicleId || 'Unknown Vehicle',
                                value: vehicle.vehicleId || vehicle.VehicleId || 'Unknown Vehicle'
                            }))}
                            labelField="label"
                            valueField="value"
                            placeholder="Vehicle number"
                            value={vehicleNumber}
                            onChange={(item) => setVehicleNumber(item.value)}
                            style={styles.locationVehicle_dropdown}
                        />
                    </View>

                    <View style={styles.locationVehicle_dropdownContainer}>
                        <Dropdown
                            data={employeeList.map(employee => ({
                                label: `${employee.UserName} - ${employee.userid}`,
                                value: employee.userid
                            }))}
                            labelField="label"
                            valueField="value"
                            placeholder="Driver"
                            value={driverName}
                            onChange={(item) => setDriverName(item.value)}
                            style={styles.locationVehicle_dropdown}
                        />
                    </View>
                </View>

                {/* Row 5: Remarks, Remove Button */}
                <View style={styles.locationVehicle_inputRow}>
                    <TextInput
                        style={[styles.locationVehicle_textField, { flex: 3 }]}
                        placeholder="Add remarks"
                        value={remarks}
                        onChangeText={setRemarks}
                        multiline
                    />

                    <TouchableOpacity style={styles.locationVehicle_removeButton}>
                        <Text style={styles.locationVehicle_buttonText}>Remove</Text>
                    </TouchableOpacity>
                </View>
            </View>


            {/* Item Details Container */}
            <View style={styles.itemDetails_container}>
                {/* Row 1: Item Details Title */}
                <View style={styles.itemDetails_row}>
                    <Text style={styles.itemDetails_title}>Item details</Text>
                </View>

                {/* Row 2: Choose Location, Select Button, Choose Batch, Select Button, Select Port */}
                <View style={styles.itemDetails_inputRow}>
                    <TextInput
                        style={styles.itemDetails_textField}
                        placeholder="Choose Item"
                        value={itemName}
                        onChangeText={setItemName}
                    />

                    <TouchableOpacity
                        style={styles.itemDetails_selectButton}
                        onPress={handleItemSelectPress}
                    >
                        <Text style={styles.itemDetails_buttonText}>Select</Text>
                    </TouchableOpacity>

                    {/* Item Selection Modal */}
                    <Modal visible={modalVisible} transparent animationType="slide">
                        <View style={styles.modalOverlay}>
                            <View style={styles.modalContainer}>
                                <View style={styles.modalHeader}>
                                    <Text style={styles.modalTitle}>Select Item</Text>
                                    <TouchableOpacity
                                        style={styles.modalCloseButton}
                                        onPress={() => setModalVisible(false)}
                                    >
                                        <Text style={styles.modalCloseButtonText}>×</Text>
                                    </TouchableOpacity>
                                </View>

                                {/* Search Input */}
                                <TextInput
                                    style={styles.modalSearchInput}
                                    placeholder="Search items..."
                                    value={searchText}
                                    onChangeText={setSearchText}
                                />

                                {/* Item List */}
                                <FlatList
                                    data={itemList.filter(item =>
                                        item.ItemName?.toLowerCase().includes(searchText.toLowerCase()) ||
                                        item.ItemID?.toLowerCase().includes(searchText.toLowerCase())
                                    )}
                                    keyExtractor={(item) => item.ItemID}
                                    renderItem={({ item }) => (
                                        <TouchableOpacity
                                            style={styles.modalBranchItem}
                                            onPress={() => handleItemSelect(item)}
                                        >
                                            <Text style={styles.modalBranchId}>{item.ItemID}</Text>
                                            <Text style={styles.modalBranchName}>{item.ItemName}</Text>
                                        </TouchableOpacity>
                                    )}
                                    style={styles.modalBranchList}
                                />
                            </View>
                        </View>
                    </Modal>

                    {batchEnabled && (
                        <>
                            <TextInput
                                style={styles.itemDetails_textField}
                                placeholder="Choose batch"
                                value={itemBatch}
                                editable={false}
                            />

                            <TouchableOpacity
                                style={styles.itemDetails_selectButton}
                                onPress={() => setBatchModalVisible(true)}
                            >
                                <Text style={styles.itemDetails_buttonText}>Select</Text>
                            </TouchableOpacity>

                            {/* Batch Selection Modal */}
                            <Modal visible={batchModalVisible} transparent animationType="slide">
                                <View style={styles.modalOverlay}>
                                    <View style={styles.modalContainer}>
                                        <View style={styles.modalHeader}>
                                            <Text style={styles.modalTitle}>Select Batch</Text>
                                            <TouchableOpacity
                                                style={styles.modalCloseButton}
                                                onPress={() => setBatchModalVisible(false)}
                                            >
                                                <Text style={styles.modalCloseButtonText}>×</Text>
                                            </TouchableOpacity>
                                        </View>

                                        <FlatList
                                            data={batchList}
                                            keyExtractor={(item, index) => index.toString()}
                                            renderItem={({ item }) => (
                                                <TouchableOpacity
                                                    style={styles.modalBranchItem}
                                                    onPress={() => handleBatchSelect(item)}
                                                >
                                                    <Text style={styles.modalBranchId}>{item.BatchNumber}</Text>
                                                    <Text style={styles.modalBranchName}>Available</Text>
                                                </TouchableOpacity>
                                            )}
                                            style={styles.modalBranchList}
                                        />
                                    </View>
                                </View>
                            </Modal>
                        </>
                    )}

                    <View style={styles.itemDetails_dropdownContainer}>
                        <Dropdown
                            data={[
                                { label: 'Port 1', value: 'Port 1' },
                                { label: 'Port 2', value: 'Port 2' },
                                { label: 'Port 3', value: 'Port 3' },
                            ]}
                            labelField="label"
                            valueField="value"
                            placeholder="Select port"
                            value={selectedPort}
                            onChange={(item) => setSelectedPort(item.value)}
                            style={styles.itemDetails_dropdown}
                        />
                    </View>
                </View>

                {/* Row 3: Wt(kg), Nos */}
                <View style={styles.itemDetails_inputRow}>
                    <TextInput
                        style={[
                            styles.itemDetails_textField,
                            { backgroundColor: (sellByWeight) ? 'white' : '#f0f0f0' }
                        ]}
                        placeholder="Wt(kg)"
                        value={itemWeight}
                        onChangeText={setItemWeight}
                        keyboardType="numeric"
                        editable={sellByWeight}
                    />

                    <TextInput
                        style={[
                            styles.itemDetails_textField,
                            { backgroundColor: (!sellByWeight || altQtyEnabled) ? 'white' : '#f0f0f0' }
                        ]}
                        placeholder="Nos"
                        value={itemNos}
                        onChangeText={setItemNos}
                        keyboardType="numeric"
                        editable={!sellByWeight || altQtyEnabled}
                    />

                    <TextInput
                        style={styles.itemDetails_textField}
                        placeholder="Remarks"
                        value={remarks}
                        onChangeText={setRemarks}
                    />
                </View>

                {/* Row 4: Add Button, Remove Button */}
                <View style={styles.itemDetails_buttonRow}>
                    <TouchableOpacity
                        style={styles.itemDetails_addButton}
                        onPress={handleAddItem}
                    >
                        <Text style={styles.itemDetails_buttonText}>Add</Text>
                    </TouchableOpacity>

                    <TouchableOpacity
                        style={styles.itemDetails_removeButton}
                        onPress={handleClearFields}
                    >
                        <Text style={styles.itemDetails_buttonText}>Clear</Text>
                    </TouchableOpacity>
                </View>

                {/* Row 5: Table */}
                <View style={styles.itemDetails_tableContainer}>
                    <DataTable>
                        <DataTable.Header style={styles.itemDetails_tableHeader}>
                            <DataTable.Title><Text style={styles.itemDetails_tableHeaderText}>Select</Text></DataTable.Title>
                            <DataTable.Title><Text style={styles.itemDetails_tableHeaderText}>Line Number</Text></DataTable.Title>
                            <DataTable.Title><Text style={styles.itemDetails_tableHeaderText}>Item ID</Text></DataTable.Title>
                            <DataTable.Title><Text style={styles.itemDetails_tableHeaderText}>Item Name</Text></DataTable.Title>
                            <DataTable.Title><Text style={styles.itemDetails_tableHeaderText}>Batch Number</Text></DataTable.Title>
                            <DataTable.Title><Text style={styles.itemDetails_tableHeaderText}>Nos</Text></DataTable.Title>
                            <DataTable.Title><Text style={styles.itemDetails_tableHeaderText}>Kgs</Text></DataTable.Title>
                            <DataTable.Title><Text style={styles.itemDetails_tableHeaderText}>Remarks</Text></DataTable.Title>
                        </DataTable.Header>

                        {tableData.map((item) => (
                            <DataTable.Row key={item.id}>
                                <DataTable.Cell>
                                    <CheckBox
                                        value={selectedRows.includes(item.id)}
                                        onValueChange={(newValue) => {
                                            if (newValue) {
                                                setSelectedRows([...selectedRows, item.id]);
                                            } else {
                                                setSelectedRows(selectedRows.filter(id => id !== item.id));
                                            }
                                        }}
                                    />
                                </DataTable.Cell>
                                <DataTable.Cell>{item.lineNo}</DataTable.Cell>
                                <DataTable.Cell>{item.itemId}</DataTable.Cell>
                                <DataTable.Cell>{item.itemName}</DataTable.Cell>
                                <DataTable.Cell>{item.batch}</DataTable.Cell>
                                <DataTable.Cell>{item.nos}</DataTable.Cell>
                                <DataTable.Cell>{item.kgs}</DataTable.Cell>
                                <DataTable.Cell>{item.remarks}</DataTable.Cell>
                            </DataTable.Row>
                        ))}
                    </DataTable>
                </View>

                {/* Row 6: Delete Selected Row Button */}
                <View style={styles.itemDetails_deleteButtonContainer}>
                    <TouchableOpacity
                        style={styles.itemDetails_deleteButton}
                        onPress={deleteSelectedItems}
                    >
                        <Text style={styles.itemDetails_buttonText}>Delete Selected Row</Text>
                    </TouchableOpacity>
                </View>
            </View>






            {/* Location Selection Modal */}
            <Modal
                visible={isLocationModalVisible}
                transparent={true}
                animationType="slide"
                onRequestClose={closeLocationModal}
            >
                <View style={styles.modalOverlay}>
                    <View style={styles.modalContainer}>
                        <View style={styles.modalHeader}>
                            <Text style={styles.modalTitle}>Select Location</Text>
                            <TouchableOpacity
                                style={styles.modalCloseButton}
                                onPress={closeLocationModal}
                            >
                                <Text style={styles.modalCloseButtonText}>×</Text>
                            </TouchableOpacity>
                        </View>

                        {loadingBranches ? (
                            <View style={styles.modalLoadingContainer}>
                                <Text style={styles.modalLoadingText}>Loading branches...</Text>
                            </View>
                        ) : (
                            <FlatList
                                data={branchList}
                                keyExtractor={(item) => item.branchID}
                                renderItem={({ item }) => (
                                    <TouchableOpacity
                                        style={styles.modalBranchItem}
                                        onPress={() => handleBranchSelection(item)}
                                    >
                                        <Text style={styles.modalBranchId}>{item.branchID}</Text>
                                        <Text style={styles.modalBranchName}>{item.branchName}</Text>
                                    </TouchableOpacity>
                                )}
                                style={styles.modalBranchList}
                            />
                        )}
                    </View>
                </View>
            </Modal>

        </View>
    );


};

const styles = StyleSheet.create({
    // ==================== COMMON STYLES ====================
    container: {
        flex: 1,
    },

    // ==================== APP BAR STYLES ====================
    appBar_container: {
        backgroundColor: '#02096A',
        paddingTop: 10,
        paddingBottom: 10,
    },
    appBar_branchRow: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        paddingHorizontal: 10,
        marginBottom: 5,
    },
    appBar_branchText: {
        color: 'white',
        fontWeight: 'bold',
        fontSize: 14,
    },

    // ==================== MENU BUTTON STYLES ====================
    menu_row: {
        paddingHorizontal: 5,
        alignItems: 'center',
    },
    menu_button: {
        paddingHorizontal: 10,
        paddingVertical: 5,
        marginHorizontal: 5,
        borderRadius: 5,
    },
    menu_text: {
        fontSize: 14,
        fontWeight: 'bold',
    },

    // ==================== PAGE CONTENT STYLES ====================
    pageContent: {
        flex: 1,
        justifyContent: 'center',
        alignItems: 'center',
        backgroundColor: '#E6E6E6',
    },
    title: {
        fontSize: 24,
        fontWeight: 'bold',
    },

    // ==================== SCROLL OPTIONS STYLES ====================
    scrollOptions_container: {
        backgroundColor: '#E6E6E6',
        paddingVertical: 8,
        marginTop: 0, // Ensure no margin at the top
    },
    scrollOptions_row: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        paddingHorizontal: 10,
    },
    scrollOptions_backContainer: {
        flexDirection: 'row',
        alignItems: 'center',
    },
    scrollOptions_backArrow: {
        fontSize: 20,
        fontWeight: 'bold',
        marginRight: 5,
    },
    scrollOptions_screenTitle: {
        fontSize: 22,
        fontWeight: 'bold',
        color: '#02096A',
    },
    scrollOptions_buttonsContainer: {
        flexDirection: 'row',
        flex: 1,
        justifyContent: 'flex-end',
    },
    scrollOptions_buttonWrapper: {
        width: '22%',
        marginHorizontal: 5,
    },
    scrollOptions_button: {
        height: 60,
        justifyContent: 'center',
        alignItems: 'center',
        borderRadius: 10,
        
    },
    scrollOptions_buttonText: {
        fontSize: 18,
        fontWeight: 'bold',
    },

    // ==================== INDENT DETAILS STYLES ====================
    indentDetails_container: {
        backgroundColor: '#EBEBEB',
        padding: 12,
        marginHorizontal: 8,
        marginTop: 8,
        marginBottom: 4,
        borderRadius: 10,
    },
    indentDetails_row: {
        marginBottom: 8,
    },
    indentDetails_title: {
        fontSize: 16,
        fontWeight: 'bold',
        color: 'black',
        marginBottom: 5,
    },
    indentDetails_transferTypeContainer: {
        flex: 1,
        minWidth: 120,
    },
    indentDetails_dropdown: {
        height: 50,
        backgroundColor: 'white',
        borderRadius: 8,
        paddingHorizontal: 10,
        borderWidth: 1,
        borderColor: '#ccc',
    },
    indentDetails_inputRow: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        flexWrap: 'wrap',
        gap: 10,
        marginBottom: 8,
    },
    indentDetails_textField: {
        flex: 1,
        height: 50,
        backgroundColor: 'white',
        borderRadius: 8,
        paddingHorizontal: 10,
        borderWidth: 1,
        borderColor: '#ccc',
        minWidth: 120,
    },
    indentDetails_selectButton: {
        height: 50,
        backgroundColor: '#041C44',
        borderRadius: 8,
        justifyContent: 'center',
        alignItems: 'center',
        paddingHorizontal: 15,
        borderWidth: 1,
        borderColor: '#FDC500',
    },
    indentDetails_selectButtonText: {
        color: 'white',
        fontWeight: 'bold',
        fontSize: 14,
    },

    // ==================== LOCATION & VEHICLE DETAILS STYLES ====================
    locationVehicle_container: {
        backgroundColor: '#EBEBEB',
        padding: 12,
        marginHorizontal: 8,
        marginTop: 4,
        marginBottom: 4,
        borderRadius: 10,
    },
    locationVehicle_row: {
        marginBottom: 8,
    },
    locationVehicle_title: {
        fontSize: 16,
        fontWeight: 'bold',
        color: 'black',
        marginBottom: 5,
    },
    locationVehicle_inputRow: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        flexWrap: 'wrap',
        gap: 10,
        marginBottom: 8,
    },
    locationVehicle_textField: {
        flex: 1,
        height: 50,
        backgroundColor: 'white',
        borderRadius: 8,
        paddingHorizontal: 10,
        borderWidth: 1,
        borderColor: '#ccc',
        minWidth: 120,
    },
    locationVehicle_dropdownContainer: {
        flex: 1,
        minWidth: 120,
    },
    locationVehicle_dropdown: {
        height: 50,
        backgroundColor: 'white',
        borderRadius: 8,
        paddingHorizontal: 10,
        borderWidth: 1,
        borderColor: '#ccc',
    },
    locationVehicle_selectButton: {
        height: 50,
        backgroundColor: '#041C44',
        borderRadius: 8,
        justifyContent: 'center',
        alignItems: 'center',
        paddingHorizontal: 15,
        borderWidth: 1,
        borderColor: '#FDC500',
    },
    locationVehicle_transitButton: {
        height: 50,
        borderRadius: 8,
        justifyContent: 'center',
        alignItems: 'center',
        paddingHorizontal: 15,
        borderWidth: 1,
        borderColor: '#FDC500',
    },
    locationVehicle_newButton: {
        height: 50,
        backgroundColor: '#097215',
        borderRadius: 8,
        justifyContent: 'center',
        alignItems: 'center',
        paddingHorizontal: 15,
        borderWidth: 1,
        borderColor: '#FDC500',
    },
    locationVehicle_removeButton: {
        height: 50,
        backgroundColor: '#FF0000',
        borderRadius: 8,
        justifyContent: 'center',
        alignItems: 'center',
        paddingHorizontal: 15,
        borderWidth: 1,
        borderColor: '#FDC500',
    },
    locationVehicle_buttonText: {
        color: 'white',
        fontWeight: 'bold',
        fontSize: 14,
    },

    // ==================== ITEM DETAILS STYLES ====================
    itemDetails_container: {
        backgroundColor: '#EBEBEB',
        padding: 12,
        marginHorizontal: 8,
        marginTop: 4,
        marginBottom: 8,
        borderRadius: 10,
    },
    itemDetails_row: {
        marginBottom: 8,
    },
    itemDetails_title: {
        fontSize: 16,
        fontWeight: 'bold',
        color: 'black',
        marginBottom: 5,
    },
    itemDetails_inputRow: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        flexWrap: 'wrap',
        gap: 10,
        marginBottom: 8,
    },
    itemDetails_textField: {
        flex: 1,
        height: 50,
        backgroundColor: 'white',
        borderRadius: 8,
        paddingHorizontal: 10,
        borderWidth: 1,
        borderColor: '#ccc',
        minWidth: 100,
    },
    itemDetails_dropdownContainer: {
        flex: 1,
        minWidth: 120,
    },
    itemDetails_dropdown: {
        height: 50,
        backgroundColor: 'white',
        borderRadius: 8,
        paddingHorizontal: 10,
        borderWidth: 1,
        borderColor: '#ccc',
    },
    itemDetails_buttonRow: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        marginBottom: 8,
    },
    itemDetails_selectButton: {
        height: 50,
        backgroundColor: '#041C44',
        borderRadius: 8,
        justifyContent: 'center',
        alignItems: 'center',
        paddingHorizontal: 15,
        borderWidth: 1,
        borderColor: '#FDC500',
    },
    itemDetails_addButton: {
        flex: 1,
        height: 50,
        backgroundColor: '#02720F',
        borderRadius: 8,
        justifyContent: 'center',
        alignItems: 'center',
        marginRight: 10,
        borderWidth: 1,
        borderColor: '#FDC500',
    },
    itemDetails_removeButton: {
        flex: 1,
        height: 50,
        backgroundColor: '#FF0000',
        borderRadius: 8,
        justifyContent: 'center',
        alignItems: 'center',
        marginLeft: 10,
        borderWidth: 1,
        borderColor: '#FDC500',
    },
    itemDetails_buttonText: {
        color: 'white',
        fontWeight: 'bold',
        fontSize: 14,
    },
    itemDetails_tableContainer: {
        marginBottom: 8,
        borderWidth: 1,
        borderColor: '#ccc',
        borderRadius: 8,
        backgroundColor: 'white',
    },
    itemDetails_tableHeader: {
        backgroundColor: '#041C44',
    },
    itemDetails_tableHeaderText: {
        color: 'white',
        fontWeight: 'bold',
        fontSize: 14,
    },
    itemDetails_deleteButtonContainer: {
        alignItems: 'center',
        marginBottom: 8,
    },
    itemDetails_deleteButton: {
        height: 50,
        backgroundColor: '#FF0000',
        borderRadius: 8,
        justifyContent: 'center',
        alignItems: 'center',
        paddingHorizontal: 20,
        borderWidth: 1,
        borderColor: '#FDC500',
    },

    // ==================== MODAL STYLES ====================
    modalOverlay: {
        flex: 1,
        backgroundColor: 'rgba(0, 0, 0, 0.5)',
        justifyContent: 'center',
        alignItems: 'center',
    },
    modalContainer: {
        backgroundColor: 'white',
        borderRadius: 10,
        padding: 20,
        width: '80%',
        maxHeight: '70%',
    },
    modalHeader: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        marginBottom: 20,
        borderBottomWidth: 1,
        borderBottomColor: '#E0E0E0',
        paddingBottom: 10,
    },
    modalTitle: {
        fontSize: 18,
        fontWeight: 'bold',
        color: '#02096A',
    },
    modalCloseButton: {
        width: 30,
        height: 30,
        borderRadius: 15,
        backgroundColor: '#FF0000',
        justifyContent: 'center',
        alignItems: 'center',
    },
    modalCloseButtonText: {
        color: 'white',
        fontSize: 18,
        fontWeight: 'bold',
    },
    modalLoadingContainer: {
        padding: 20,
        alignItems: 'center',
    },
    modalLoadingText: {
        fontSize: 16,
        color: '#666',
    },
    modalBranchList: {
        maxHeight: 400,
    },
    modalBranchItem: {
        padding: 15,
        borderBottomWidth: 1,
        borderBottomColor: '#E0E0E0',
        backgroundColor: '#F8F8F8',
        marginBottom: 5,
        borderRadius: 8,
    },
    modalBranchId: {
        fontSize: 16,
        fontWeight: 'bold',
        color: '#02096A',
        marginBottom: 5,
    },
    modalBranchName: {
        fontSize: 14,
        color: '#666',
    },
    modalSearchInput: {
        height: 40,
        borderWidth: 1,
        borderColor: '#ccc',
        borderRadius: 8,
        paddingHorizontal: 10,
        marginBottom: 15,
        backgroundColor: 'white',
    },
});

export default ReceivingScreen;
