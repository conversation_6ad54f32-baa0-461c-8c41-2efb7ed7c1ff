// fetchBillsAPI.js
import axios from 'axios';

export const fetchBillsByDateNew = async (branchId, stage, fromDate, toDate, bearerToken) => {
    try {
        const response = await axios.get(
            `https://retailuat.abisibg.com/api/v1/fetchsale`,
            {
                params: {
                    BranchId: branchId,
                    stage,
                    FromDate: fromDate,
                    ToDate: toDate,
                },
                headers: {
                    Authorization: `Bearer ${bearerToken}`,
                },
            }
        );
        return response.data;
    } catch (error) {
        console.error('Error fetching sales bills:', error);
        return [];
    }
};
